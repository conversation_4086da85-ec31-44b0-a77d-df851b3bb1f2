﻿/**
 * Compare token metrics between OKX and Lamboo data sources
 * Usage: node compareTokenMetrics.js
 * 
 * Compares data from:
 * - OKXMetricData.json (from OKX API)
 * - lambooTokenData.json (from Lamboo API)
 * 
 * Output files:
 * - token_metrics_compare.json (detailed comparison)
 * - token_metrics_compare.csv (summary table)
 */

const fs = require('fs');
const path = require('path');

// === HELPER FUNCTIONS ===
const toNum = (x) => (x === null || x === undefined || x === '' ? null : Number(x));

function loadJSON(filePath) {
  try {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
  } catch (err) {
    console.error(`Error loading ${filePath}:`, err.message);
    return [];
  }
}

// Calculate percentage difference based on Lamboo: (lamboo - okx) / okx * 100
// Positive = Lamboo higher (green), Negative = Lamboo lower (red)
function pctDiff(okxVal, lambooVal) {
  const o = toNum(okxVal) || 0;
  const l = toNum(lambooVal) || 0;
  const EPS = 1e-6;

  if (Math.abs(o) < EPS) return Math.abs(l) < EPS ? 0 : null;
  return ((l - o) / Math.abs(o)) * 100;
}

// Build CSV content
function toCSV(rows) {
  const header = [
    'addressToken', 'symbol', 'tokenName', 'timeframe',
    'okx_volume', 'lamboo_volume', 'volume_delta', 'volume_pct',
    'okx_txns', 'lamboo_txns', 'txns_delta', 'txns_pct',
    'okx_buys', 'lamboo_buys', 'buys_delta', 'buys_pct',
    'okx_sells', 'lamboo_sells', 'sells_delta', 'sells_pct',
    'okx_buyVol', 'lamboo_buyVol', 'buyVol_delta', 'buyVol_pct',
    'okx_sellVol', 'lamboo_sellVol', 'sellVol_delta', 'sellVol_pct',
    'okx_netVol', 'lamboo_netVol', 'netVol_delta', 'netVol_pct',
    'okx_totalTraders', 'lamboo_markers', 'totalTraders_delta', 'totalTraders_pct',
    'okx_buyTraders', 'lamboo_buyers', 'buyTraders_delta', 'buyTraders_pct',
    'okx_sellTraders', 'lamboo_sellers', 'sellTraders_delta', 'sellTraders_pct',
    'status'
  ];
  
  const esc = (v) => {
    if (v === null || v === undefined) return '';
    const s = String(v);
    return /[",\n]/.test(s) ? '"' + s.replace(/"/g, '""') + '"' : s;
  };
  
  const pctFmt = (v) => (v === null || v === undefined || !Number.isFinite(v) ? '' : v.toFixed(2));
  
  const lines = [header.join(',')];
  
  for (const r of rows) {
    for (const tf of r.timeframes) {
      lines.push([
        esc(r.address), esc(r.symbol), esc(r.tokenName), esc(tf.timeframe),
        esc(tf.okx?.volume), esc(tf.lamboo?.volume), esc(tf.delta?.volume), esc(pctFmt(tf.pct?.volume)),
        esc(tf.okx?.txns), esc(tf.lamboo?.txns), esc(tf.delta?.txns), esc(pctFmt(tf.pct?.txns)),
        esc(tf.okx?.buys), esc(tf.lamboo?.buys), esc(tf.delta?.buys), esc(pctFmt(tf.pct?.buys)),
        esc(tf.okx?.sells), esc(tf.lamboo?.sells), esc(tf.delta?.sells), esc(pctFmt(tf.pct?.sells)),
        esc(tf.okx?.buyVol), esc(tf.lamboo?.buyVol), esc(tf.delta?.buyVol), esc(pctFmt(tf.pct?.buyVol)),
        esc(tf.okx?.sellVol), esc(tf.lamboo?.sellVol), esc(tf.delta?.sellVol), esc(pctFmt(tf.pct?.sellVol)),
        esc(tf.okx?.netVol), esc(tf.lamboo?.netVol), esc(tf.delta?.netVol), esc(pctFmt(tf.pct?.netVol)),
        esc(tf.okx?.traders), esc(tf.lamboo?.traders), esc(tf.delta?.traders), esc(pctFmt(tf.pct?.traders)),
        esc(tf.okx?.buyTraders), esc(tf.lamboo?.buyers), esc(tf.delta?.buyTraders), esc(pctFmt(tf.pct?.buyTraders)),
        esc(tf.okx?.sellTraders), esc(tf.lamboo?.sellers), esc(tf.delta?.sellTraders), esc(pctFmt(tf.pct?.sellTraders)),
        esc(tf.status)
      ].join(','));
    }
  }
  
  return lines.join('\n');
}

// Calculate timeframe statistics for HTML
function calculateTimeframeStats(rows) {
  const timeframeStats = {
    '5m': { total: 0, match: 0, good: 0, mismatch: 0 },
    '1h': { total: 0, match: 0, good: 0, mismatch: 0 },
    '24h': { total: 0, match: 0, good: 0, mismatch: 0 }
  };

  rows.forEach(token => {
    token.timeframes.forEach(tf => {
      if (timeframeStats[tf.timeframe]) {
        timeframeStats[tf.timeframe].total++;
        if (tf.status === 'MATCH') timeframeStats[tf.timeframe].match++;
        else if (tf.status === 'GOOD') timeframeStats[tf.timeframe].good++;
        else if (tf.status === 'MISMATCH') timeframeStats[tf.timeframe].mismatch++;
      }
    });
  });

  return timeframeStats;
}

// Generate HTML for timeframe statistics
function generateTimeframeStatsHTML(timeframeStats) {
  let html = `
        <table style="width: 100%; margin-top: 10px;">
            <thead>
                <tr>
                    <th>Timeframe</th>
                    <th>Total</th>
                    <th>Match</th>
                    <th>Good</th>
                    <th>Mismatch</th>
                </tr>
            </thead>
            <tbody>`;

  Object.keys(timeframeStats).forEach(tf => {
    const stats = timeframeStats[tf];
    if (stats.total > 0) {
      html += `
                <tr>
                    <td><strong>${tf.toUpperCase()}</strong></td>
                    <td class="number">${stats.total}</td>
                    <td class="number"><span class="match" style="padding: 2px 6px; border-radius: 3px;">${stats.match} (${(stats.match/stats.total*100).toFixed(1)}%)</span></td>
                    <td class="number"><span class="good" style="padding: 2px 6px; border-radius: 3px;">${stats.good} (${(stats.good/stats.total*100).toFixed(1)}%)</span></td>
                    <td class="number"><span class="mismatch" style="padding: 2px 6px; border-radius: 3px;">${stats.mismatch} (${(stats.mismatch/stats.total*100).toFixed(1)}%)</span></td>
                </tr>`;
    }
  });

  html += `
            </tbody>
        </table>`;
  return html;
}

// Build HTML table with colors
function toHTML(rows) {
  const getStatusColor = (status) => {
    switch (status) {
      case 'MATCH': return '#d4edda'; // Light green
      case 'GOOD': return '#d1ecf1'; // Light blue
      case 'MISMATCH': return '#f8d7da'; // Light red
      case 'UNMATCHED': return '#fff3cd'; // Light yellow
      default: return '#ffffff';
    }
  };

  const getStatusTextColor = (status) => {
    switch (status) {
      case 'MATCH': return '#155724'; // Dark green
      case 'GOOD': return '#0c5460'; // Dark blue
      case 'MISMATCH': return '#721c24'; // Dark red
      case 'UNMATCHED': return '#856404'; // Dark yellow
      default: return '#000000';
    }
  };

  // Format percentage with color and sign
  const formatPctWithColor = (pct) => {
    if (pct === null || pct === undefined || !Number.isFinite(pct)) return 'N/A';

    const value = pct.toFixed(1);
    const sign = pct > 0 ? '+' : '';
    const color = pct > 0 ? '#28a745' : '#dc3545'; // Green for positive, red for negative

    return `<span style="color: ${color}; font-weight: bold;">${sign}${value}%</span>`;
  };

  let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>OKX vs Lamboo Token Metrics Comparison</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; text-align: center; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #f2f2f2; font-weight: bold; position: sticky; top: 0; }
        .status-cell { font-weight: bold; text-align: center; }
        .number { text-align: right; }
        .pool-name { max-width: 150px; word-wrap: break-word; }
        .address { font-family: monospace; font-size: 10px; word-break: break-all; white-space: normal; }
        .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .match { background-color: #d4edda; color: #155724; }
        .good { background-color: #d1ecf1; color: #0c5460; }
        .mismatch { background-color: #f8d7da; color: #721c24; }
        .unmatched { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>≡ƒöì OKX vs Lamboo Token Metrics Comparison</h1>
    <div class="summary">
        <h3>≡ƒôè Summary Statistics</h3>
        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Tolerance:</strong> 10% for all metrics (volume, transactions, buy/sell volumes, traders, etc.)</p>
        <p><strong>Percentage Calculation:</strong> Based on Lamboo data as reference. <span style="color: #28a745; font-weight: bold;">+Green</span> = Lamboo higher than OKX, <span style="color: #dc3545; font-weight: bold;">-Red</span> = Lamboo lower than OKX</p>
    </div>

    <div class="summary">
        <h3>Statistics by Timeframe</h3>
        ${generateTimeframeStatsHTML(calculateTimeframeStats(rows))}
    </div>

    <table>
        <thead>
            <tr>
                <th>Token</th>
                <th>Address</th>
                <th>Timeframe</th>
                <th>OKX Volume</th>
                <th>Lamboo Volume</th>
                <th>Volume &Delta;%</th>
                <th>OKX Txns</th>
                <th>Lamboo Txns</th>
                <th>Txns &Delta;%</th>
                <th>OKX Buy Vol</th>
                <th>Lamboo Buy Vol</th>
                <th>Buy Vol &Delta;%</th>
                <th>OKX Sell Vol</th>
                <th>Lamboo Sell Vol</th>
                <th>Sell Vol &Delta;%</th>
                <th>OKX Buy Traders</th>
                <th>Lamboo Buyers</th>
                <th>Buy Traders &Delta;%</th>
                <th>OKX Sell Traders</th>
                <th>Lamboo Sellers</th>
                <th>Sell Traders &Delta;%</th>
                <th>OKX Total Traders</th>
                <th>Lamboo Markers</th>
                <th>Total Traders &Delta;%</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>`;

  for (const r of rows) {
    for (const tf of r.timeframes) {
      const statusColor = getStatusColor(tf.status);
      const statusTextColor = getStatusTextColor(tf.status);

      const volumePct = formatPctWithColor(tf.pct?.volume);
      const txnsPct = formatPctWithColor(tf.pct?.txns);
      const buyVolPct = formatPctWithColor(tf.pct?.buyVol);
      const sellVolPct = formatPctWithColor(tf.pct?.sellVol);
      const buyTradersPct = formatPctWithColor(tf.pct?.buyTraders);
      const sellTradersPct = formatPctWithColor(tf.pct?.sellTraders);
      const tradersPct = formatPctWithColor(tf.pct?.traders);

      html += `
            <tr style="background-color: ${statusColor};">
                <td><strong>${r.symbol}</strong><br><small>${r.tokenName}</small></td>
                <td class="address">${r.address}</td>
                <td><strong>${tf.timeframe}</strong></td>
                <td class="number">$${tf.okx?.volume?.toLocaleString() || 'N/A'}</td>
                <td class="number">$${tf.lamboo?.volume?.toLocaleString() || 'N/A'}</td>
                <td class="number">${volumePct}</td>
                <td class="number">${tf.okx?.txns?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tf.lamboo?.txns?.toLocaleString() || 'N/A'}</td>
                <td class="number">${txnsPct}</td>
                <td class="number">$${tf.okx?.buyVol?.toLocaleString() || 'N/A'}</td>
                <td class="number">$${tf.lamboo?.buyVol?.toLocaleString() || 'N/A'}</td>
                <td class="number">${buyVolPct}</td>
                <td class="number">$${tf.okx?.sellVol?.toLocaleString() || 'N/A'}</td>
                <td class="number">$${tf.lamboo?.sellVol?.toLocaleString() || 'N/A'}</td>
                <td class="number">${sellVolPct}</td>
                <td class="number">${tf.okx?.buyTraders?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tf.lamboo?.buyers?.toLocaleString() || 'N/A'}</td>
                <td class="number">${buyTradersPct}</td>
                <td class="number">${tf.okx?.sellTraders?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tf.lamboo?.sellers?.toLocaleString() || 'N/A'}</td>
                <td class="number">${sellTradersPct}</td>
                <td class="number">${tf.okx?.traders?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tf.lamboo?.traders?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tradersPct}</td>
                <td class="status-cell" style="color: ${statusTextColor}; font-weight: bold;">${tf.status}</td>
            </tr>`;
    }
  }

  html += `
        </tbody>
    </table>

    <div class="summary">
        <h3>≡ƒÄ¿ Color Legend</h3>
        <p><span class="match" style="padding: 5px 10px; border-radius: 3px;">MATCH</span> - Data matches within tolerance</p>
        <p><span class="good" style="padding: 5px 10px; border-radius: 3px;">GOOD</span> - Lamboo data is higher than OKX (positive direction)</p>
        <p><span class="mismatch" style="padding: 5px 10px; border-radius: 3px;">MISMATCH</span> - Data differs significantly (negative direction)</p>
        <p><span class="unmatched" style="padding: 5px 10px; border-radius: 3px;">UNMATCHED</span> - Data only available from one source</p>
    </div>
</body>
</html>`;

  return html;
}

// === MAIN LOGIC ===
function main() {
  const __dirname = path.dirname(__filename);
  
  // Load data files
  const okxData = loadJSON(path.join(__dirname, '../results/OKXMetricData.json'));
  const lambooData = loadJSON(path.join(__dirname, '../results/lambooTokenData.json'));
  
  console.log(`Loaded ${okxData.length} OKX records and ${lambooData.length} Lamboo records`);
  
  // Create maps by token address
  const okxMap = new Map();
  okxData.forEach(item => {
    okxMap.set(item.address.toLowerCase(), item);
  });
  
  const lambooMap = new Map();
  lambooData.forEach(item => {
    lambooMap.set(item.address.toLowerCase(), item);
  });
  
  // Get union of all token addresses
  const allAddresses = new Set([...okxMap.keys(), ...lambooMap.keys()]);
  console.log(`Found ${allAddresses.size} unique token addresses`);
  
  const results = [];
  const tolerance = 10; // 10% tolerance for volume comparison
  
  for (const address of allAddresses) {
    const okxToken = okxMap.get(address);
    const lambooToken = lambooMap.get(address);
    
    const symbol = okxToken?.symbol || lambooToken?.symbol || 'UNKNOWN';
    const tokenName = okxToken?.name || lambooToken?.name || 'Unknown';
    
    console.log(`\nProcessing ${symbol} (${tokenName})`);
    
    const tokenResult = {
      address,
      symbol,
      tokenName,
      timeframes: []
    };
    
    // Compare each timeframe
    const timeframes = ['5m', '1h', '24h'];
    
    for (const tf of timeframes) {
      const okxTf = okxToken?.data?.[tf];
      const lambooTf = lambooToken?.data?.[tf];
      
      if (!okxTf && !lambooTf) {
        console.log(`  ${tf}: No data from both sources`);
        continue;
      }
      
      // Extract metrics
      const okxMetrics = okxTf ? {
        volume: okxTf.totalVol || 0,
        txns: okxTf.totalTxns || 0,
        buys: okxTf.buyTxns || 0,
        sells: okxTf.sellTxns || 0,
        buyVol: okxTf.buyVol || 0,
        sellVol: okxTf.sellVol || 0,
        netVol: okxTf.netVol || 0,
        traders: okxTf.totalTraders || 0,
        buyTraders: okxTf.buyTraders || 0,
        sellTraders: okxTf.sellTraders || 0,
        netBuyers: (okxTf.buyTraders || 0) - (okxTf.sellTraders || 0)
      } : null;

      const lambooMetrics = lambooTf ? {
        volume: lambooTf.volume || 0,
        txns: lambooTf.txns || 0,
        buys: lambooTf.buys || 0,
        sells: lambooTf.sells || 0,
        buyVol: lambooTf.volumeBuy || 0,
        sellVol: lambooTf.volumeSell || 0,
        netVol: (lambooTf.volumeBuy || 0) - (lambooTf.volumeSell || 0), // Calculate net volume
        traders: lambooTf.markers || 0,
        buyers: lambooTf.buyers || 0,
        sellers: lambooTf.sellers || 0,
        netBuyers: (lambooTf.buyers || 0) - (lambooTf.sellers || 0)
      } : null;
      
      // Calculate deltas and percentages
      const delta = {
        volume: okxMetrics && lambooMetrics ? okxMetrics.volume - lambooMetrics.volume : null,
        txns: okxMetrics && lambooMetrics ? okxMetrics.txns - lambooMetrics.txns : null,
        buys: okxMetrics && lambooMetrics ? okxMetrics.buys - lambooMetrics.buys : null,
        sells: okxMetrics && lambooMetrics ? okxMetrics.sells - lambooMetrics.sells : null,
        buyVol: okxMetrics && lambooMetrics ? okxMetrics.buyVol - lambooMetrics.buyVol : null,
        sellVol: okxMetrics && lambooMetrics ? okxMetrics.sellVol - lambooMetrics.sellVol : null,
        netVol: okxMetrics && lambooMetrics ? okxMetrics.netVol - lambooMetrics.netVol : null,
        traders: okxMetrics && lambooMetrics ? okxMetrics.traders - lambooMetrics.traders : null,
        buyTraders: okxMetrics && lambooMetrics ? okxMetrics.buyTraders - lambooMetrics.buyers : null,
        sellTraders: okxMetrics && lambooMetrics ? okxMetrics.sellTraders - lambooMetrics.sellers : null,
        netBuyers: okxMetrics && lambooMetrics ? okxMetrics.netBuyers - lambooMetrics.netBuyers : null
      };

      const pct = {
        volume: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.volume, lambooMetrics.volume) : null,
        txns: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.txns, lambooMetrics.txns) : null,
        buys: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.buys, lambooMetrics.buys) : null,
        sells: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.sells, lambooMetrics.sells) : null,
        buyVol: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.buyVol, lambooMetrics.buyVol) : null,
        sellVol: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.sellVol, lambooMetrics.sellVol) : null,
        netVol: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.netVol, lambooMetrics.netVol) : null,
        traders: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.traders, lambooMetrics.traders) : null,
        buyTraders: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.buyTraders, lambooMetrics.buyers) : null,
        sellTraders: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.sellTraders, lambooMetrics.sellers) : null,
        netBuyers: okxMetrics && lambooMetrics ? pctDiff(okxMetrics.netBuyers, lambooMetrics.netBuyers) : null
      };
      
      // Determine status
      let status = 'UNMATCHED';
      if (okxMetrics && lambooMetrics) {
        const metrics = ['volume', 'txns', 'buys', 'sells', 'buyVol', 'sellVol', 'netVol', 'traders', 'buyTraders', 'sellTraders', 'netBuyers'];
        const withinTolerance = metrics.every((metric) => {
          const pctVal = pct[metric];
          return Number.isFinite(pctVal) && Math.abs(pctVal) <= tolerance;
        });

        if (withinTolerance) {
          status = 'MATCH';
        } else {
          const lambooHigherVol = Number.isFinite(pct.volume) && pct.volume > 0; // Lamboo volume > OKX volume
          const lambooHigherTxns = Number.isFinite(pct.txns) && pct.txns > 0; // Lamboo txns > OKX txns

          // If both volume and txns are higher in Lamboo, it's a positive mismatch
          if (lambooHigherVol && lambooHigherTxns) {
            status = 'GOOD';
          } else {
            status = 'MISMATCH';
          }
        }
      }
      
      console.log(`  ${tf}: OKX Vol=$${okxMetrics?.volume?.toLocaleString() || 'N/A'}, Lamboo Vol=$${lambooMetrics?.volume?.toLocaleString() || 'N/A'} (${status})`);
      
      tokenResult.timeframes.push({
        timeframe: tf,
        okx: okxMetrics,
        lamboo: lambooMetrics,
        delta,
        pct,
        status
      });
    }
    
    results.push(tokenResult);
  }
  
  const sortedRows = results
    .flatMap((token) => (token.timeframes || []).map((tf) => ({
      address: token.address,
      symbol: token.symbol,
      tokenName: token.tokenName,
      timeframes: [tf]
    })))
    .sort((a, b) => {
      const volA = Number(a.timeframes[0]?.lamboo?.volume);
      const volB = Number(b.timeframes[0]?.lamboo?.volume);
      const sortA = Number.isFinite(volA) ? volA : -Infinity;
      const sortB = Number.isFinite(volB) ? volB : -Infinity;
      return sortB - sortA;
    });

  // Save JSON report
  const jsonOutput = {
    generatedAt: new Date().toISOString(),
    tolerancePercentVolume: tolerance,
    notes: 'Comparison between OKX and Lamboo token metrics. All metrics tolerance: 10% based on percentage differences.',
    tokens: results
  };

  const jsonPath = path.join(__dirname, '../results/compareVsOKX.json');
  fs.writeFileSync(jsonPath, JSON.stringify(jsonOutput, null, 2));
  console.log(`\nSaved JSON report to ${jsonPath}`);

  // Save CSV summary
  const csvContent = toCSV(sortedRows);
  const csvPath = path.join(__dirname, '../reports/compareVsOKX.csv');
  fs.writeFileSync(csvPath, csvContent, 'utf8');
  console.log(`Saved CSV summary to ${csvPath}`);

  // Save HTML report with colors
  const htmlContent = toHTML(sortedRows);
  const htmlPath = path.join(__dirname, '../reports/compareVsOKX.html');
  fs.writeFileSync(htmlPath, htmlContent, 'utf8');
  console.log(`Saved HTML report to ${htmlPath}`);

  
  // Summary statistics
  let totalComparisons = 0;
  let matchCount = 0;
  let goodCount = 0;
  let mismatchCount = 0;

  // Statistics by timeframe
  const timeframeStats = {
    '5m': { total: 0, match: 0, good: 0, mismatch: 0 },
    '1h': { total: 0, match: 0, good: 0, mismatch: 0 },
    '24h': { total: 0, match: 0, good: 0, mismatch: 0 }
  };

  results.forEach(token => {
    token.timeframes.forEach(tf => {
      totalComparisons++;

      // Update overall stats
      if (tf.status === 'MATCH') matchCount++;
      else if (tf.status === 'GOOD') goodCount++;
      else if (tf.status === 'MISMATCH') mismatchCount++;

      // Update timeframe stats
      if (timeframeStats[tf.timeframe]) {
        timeframeStats[tf.timeframe].total++;
        if (tf.status === 'MATCH') timeframeStats[tf.timeframe].match++;
        else if (tf.status === 'GOOD') timeframeStats[tf.timeframe].good++;
        else if (tf.status === 'MISMATCH') timeframeStats[tf.timeframe].mismatch++;
      }
    });
  });

  console.log('\nOverall Summary Statistics:');
  console.log(`  Total comparisons: ${totalComparisons}`);
  console.log(`  Matches: ${matchCount} (${(matchCount/totalComparisons*100).toFixed(1)}%)`);
  console.log(`  Good (Lamboo higher): ${goodCount} (${(goodCount/totalComparisons*100).toFixed(1)}%)`);
  console.log(`  Mismatches: ${mismatchCount} (${(mismatchCount/totalComparisons*100).toFixed(1)}%)`);

  console.log('\nStatistics by Timeframe:');
  Object.keys(timeframeStats).forEach(tf => {
    const stats = timeframeStats[tf];
    if (stats.total > 0) {
      console.log(`  ${tf.toUpperCase()}:`);
      console.log(`    Total: ${stats.total}`);
      console.log(`    Match: ${stats.match} (${(stats.match/stats.total*100).toFixed(1)}%)`);
      console.log(`    Good: ${stats.good} (${(stats.good/stats.total*100).toFixed(1)}%)`);
      console.log(`    Mismatch: ${stats.mismatch} (${(stats.mismatch/stats.total*100).toFixed(1)}%)`);
    }
  });
}

main();
