/**
 * Compare 24h metrics between Dexscreener and Lamboo (24h timeframe) and export JSON + CSV.
 * Usage: node compareData.js
 * Fixed paths: expects input/output files to live in the same folder as this script.
 */

const fs = require('fs');
const path = require('path');

// Convert possibly-null/empty values to number or null
const toNum = (x) => (x === null || x === undefined || x === '' ? null : Number(x));

// Calculate percentage difference: (Lamboo - Dexscreener) / Dexscreener * 100
function pctDiff(dexVal, lambooVal) {
  const d = toNum(dexVal) || 0;
  const l = toNum(lambooVal) || 0;
  const EPS = 1e-6;

  if (Math.abs(d) < EPS) return Math.abs(l) < EPS ? 0 : null;
  return ((l - d) / Math.abs(d)) * 100;
}

// Format percentage with color coding
function formatPctWithColor(pct) {
  if (pct === null || pct === undefined || !Number.isFinite(pct)) return 'N/A';
  const absVal = Math.abs(pct).toFixed(1);
  const prefix = pct > 0 ? '+' : pct < 0 ? '-' : '';
  const color = pct > 0 ? '#28a745' : '#dc3545'; // Green for positive, red for negative
  return `<span style="color: ${color}; font-weight: bold;">${prefix}${absVal}%</span>`;
}

// Calculate timeframe statistics for HTML
function calculateTimeframeStats(rows) {
  const timeframeStats = {
    '24h': { total: 0, match: 0, good: 0, mismatch: 0 }
  };

  rows.forEach(token => {
    token.timeframes.forEach(tf => {
      if (timeframeStats[tf.timeframe]) {
        timeframeStats[tf.timeframe].total++;
        if (tf.status === 'MATCH') timeframeStats[tf.timeframe].match++;
        else if (tf.status === 'GOOD') timeframeStats[tf.timeframe].good++;
        else if (tf.status === 'MISMATCH') timeframeStats[tf.timeframe].mismatch++;
      }
    });
  });

  return timeframeStats;
}

// Generate timeframe statistics HTML table
function generateTimeframeStatsHTML(timeframeStats) {
  let html = `
    <div class="summary">
        <h3>📈 Statistics by Timeframe</h3>

        <table style="width: 100%; margin-top: 10px;">
            <thead>
                <tr>
                    <th>Timeframe</th>
                    <th>Total</th>
                    <th>Match</th>
                    <th>Good</th>
                    <th>Mismatch</th>
                </tr>
            </thead>
            <tbody>`;

  Object.keys(timeframeStats).forEach(tf => {
    const stats = timeframeStats[tf];
    if (stats.total > 0) {
      html += `
                <tr>
                    <td><strong>${tf.toUpperCase()}</strong></td>
                    <td class="number">${stats.total}</td>
                    <td class="number"><span class="match" style="padding: 2px 6px; border-radius: 3px;">${stats.match} (${(stats.match/stats.total*100).toFixed(1)}%)</span></td>
                    <td class="number"><span class="good" style="padding: 2px 6px; border-radius: 3px;">${stats.good} (${(stats.good/stats.total*100).toFixed(1)}%)</span></td>
                    <td class="number"><span class="mismatch" style="padding: 2px 6px; border-radius: 3px;">${stats.mismatch} (${(stats.mismatch/stats.total*100).toFixed(1)}%)</span></td>
                </tr>`;
    }
  });

  html += `
            </tbody>
        </table>
    </div>`;

  return html;
}

// Normalize DEX id across sources (e.g., thala_v2 -> thala)
function normalizeDexId(id) {
  if (!id) return null;
  const x = String(id).toLowerCase();
  if (x === 'thala_v2' || x === 'thala-v2') return 'thala';
  return x;
}

// Aptos pair addresses on Dexscreener may include suffixes like "-0xa-<mint>"; Lamboo uses the base hash
function normalizePairAddress(addr) {
  if (!addr) return null;
  const s = String(addr).toLowerCase();
  return s.split('-')[0];
}

// Read and parse a JSON file (throws on error to fail fast)
function loadJSON(p) {
  return JSON.parse(fs.readFileSync(p, 'utf8'));
}

// Extract the 24h slice from a Dexscreener record
function pickDexs24h(rec) {
  const buys = toNum(rec?.txns?.h24?.buys) || 0;
  const sells = toNum(rec?.txns?.h24?.sells) || 0;
  const txns = buys + sells; // Dexscreener doesn't give txns total directly
  const volume = toNum(rec?.volume?.h24) || 0;
  const priceChange = rec?.priceChange?.h24 ?? null;
  const liquidityUsd = toNum(rec?.liquidityUsd);
  const marketCap = toNum(rec?.marketCap);
  const tokenSymbol = rec?.tokenSymbol || null;
  const tokenName = rec?.tokenName || null;
  return { buys, sells, txns, volume, priceChange, liquidityUsd, marketCap, tokenSymbol, tokenName };
}

// Extract the 24h slice from a Lamboo record (already 24h fields)
function pickLamboo24h(rec) {
  const buys = toNum(rec?.buys) || 0;
  const sells = toNum(rec?.sells) || 0;
  const txns = toNum(rec?.txns) || (buys + sells);
  const volume = toNum(rec?.volume) || 0;
  const tokenSymbol = rec?.sourceToken?.symbol || null;
  const tokenName = rec?.sourceToken?.name || null;
  return { buys, sells, txns, volume, tokenSymbol, tokenName };
}

// Percent difference relative to Lamboo: (Lamboo - Dex) / |Lamboo| * 100
function pctDiff(dexVal, lamVal) {
  const d = toNum(dexVal);
  const l = toNum(lamVal);
  if (d === null || l === null || !Number.isFinite(d) || !Number.isFinite(l)) return null;
  const EPS = 1e-9;
  if (Math.abs(l) < EPS) return Math.abs(d) < EPS ? 0 : null;
  return ((l - d) / Math.abs(l)) * 100;
}

// Minimal CSV builder with safe value serialization
function toCSV(rows) {
  const header = [
    'pairKey','dex','tokenSymbol','tokenName',
    'dexs_buys24','lamboo_buys24','delta_buys','delta_buys_pct',
    'dexs_sells24','lamboo_sells24','delta_sells','delta_sells_pct',
    'dexs_txns24','lamboo_txns24','delta_txns','delta_txns_pct',
    'dexs_volume24','lamboo_volume24','delta_volume','delta_volume_pct',
    'priceChange24','liquidityUsd','marketCap','status'
  ];
  const esc = (v) => {
    if (v === null || v === undefined) return '';
    const s = String(v);
    return /[",\n]/.test(s) ? '"' + s.replace(/"/g, '""') + '"' : s;
  };
  const pctFmt = (v) => {
    if (v === null || v === undefined || !Number.isFinite(v)) return '';
    const formatted = v.toFixed(2);
    return v > 0 ? `+${formatted}` : formatted;
  };
  const lines = [header.join(',')];
  for (const r of rows) {
    lines.push([
      esc(r.pairKey), esc(r.dex), esc(r.tokenSymbol), esc(r.tokenName),
      esc(r.dexs_buys24), esc(r.lamboo_buys24), esc(r.delta_buys), esc(pctFmt(r.delta_buys_pct)),
      esc(r.dexs_sells24), esc(r.lamboo_sells24), esc(r.delta_sells), esc(pctFmt(r.delta_sells_pct)),
      esc(r.dexs_txns24), esc(r.lamboo_txns24), esc(r.delta_txns), esc(pctFmt(r.delta_txns_pct)),
      esc(r.dexs_volume24), esc(r.lamboo_volume24), esc(r.delta_volume), esc(pctFmt(r.delta_volume_pct)),
      esc(r.priceChange24), esc(r.liquidityUsd), esc(r.marketCap),
      esc(r.status)
    ].join(','));
  }
  return lines.join('\n');
}

// Build HTML table with colors
function toHTML(rows) {
  const getStatusColor = (status) => {
    switch (status) {
      case 'MATCH': return '#d4edda'; // Light green
      case 'GOOD': return '#d1ecf1'; // Light blue
      case 'MISMATCH': return '#f8d7da'; // Light red
      case 'UNMATCHED': return '#fff3cd'; // Light yellow
      default: return '#ffffff';
    }
  };

  const getStatusTextColor = (status) => {
    switch (status) {
      case 'MATCH': return '#155724'; // Dark green
      case 'GOOD': return '#0c5460'; // Dark blue
      case 'MISMATCH': return '#721c24'; // Dark red
      case 'UNMATCHED': return '#856404'; // Dark yellow
      default: return '#000000';
    }
  };



  let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Dexscreener vs Lamboo Comparison</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; text-align: center; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #f2f2f2; font-weight: bold; position: sticky; top: 0; }
        .status-cell { font-weight: bold; text-align: center; }
        .number { text-align: right; }
        .pool-name { max-width: 150px; word-wrap: break-word; }
        .address { font-family: monospace; font-size: 10px; max-width: 100px; word-wrap: break-word; }
        .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .match { background-color: #d4edda; color: #155724; }
        .good { background-color: #d1ecf1; color: #0c5460; }
        .mismatch { background-color: #f8d7da; color: #721c24; }
        .unmatched { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🔍 Dexscreener vs Lamboo Market Data Comparison</h1>
    <div class="summary">
        <h3>📊 Summary Statistics</h3>
        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Tolerance:</strong> 10% for all metrics (buys, sells, txns, volume)</p>
        <p><strong>Percentage Calculation:</strong> Based on Dexscreener data as reference. <span style="color: #28a745; font-weight: bold;">+Green</span> = Lamboo higher than Dexscreener, <span style="color: #dc3545; font-weight: bold;">-Red</span> = Lamboo lower than Dexscreener</p>
    </div>

    ${generateTimeframeStatsHTML(calculateTimeframeStats(rows))}
    <table>
        <thead>
            <tr>
                <th>Token</th>
                <th>DEX</th>
                <th>Pair Key</th>
                <th>Dexs Buys</th>
                <th>Lamboo Buys</th>
                <th>Buys Δ%</th>
                <th>Dexs Sells</th>
                <th>Lamboo Sells</th>
                <th>Sells Δ%</th>
                <th>Dexs Volume</th>
                <th>Lamboo Volume</th>
                <th>Volume Δ%</th>
                <th>Price Change</th>
                <th>Liquidity</th>
                <th>Market Cap</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>`;

  for (const r of rows) {
    const statusColor = getStatusColor(r.status);
    const statusTextColor = getStatusTextColor(r.status);

    html += `
            <tr style="background-color: ${statusColor};">
                <td><strong>${r.tokenSymbol}</strong><br><small>${r.tokenName}</small></td>
                <td>${r.dex}</td>
                <td class="address">${r.pairKey}</td>
                <td class="number">${r.dexs_buys24?.toLocaleString() || 'N/A'}</td>
                <td class="number">${r.lamboo_buys24?.toLocaleString() || 'N/A'}</td>
                <td class="number">${formatPctWithColor(r.delta_buys_pct)}</td>
                <td class="number">${r.dexs_sells24?.toLocaleString() || 'N/A'}</td>
                <td class="number">${r.lamboo_sells24?.toLocaleString() || 'N/A'}</td>
                <td class="number">${formatPctWithColor(r.delta_sells_pct)}</td>
                <td class="number">$${r.dexs_volume24?.toLocaleString() || 'N/A'}</td>
                <td class="number">$${r.lamboo_volume24?.toLocaleString() || 'N/A'}</td>
                <td class="number">${formatPctWithColor(r.delta_volume_pct)}</td>
                <td class="number">${r.priceChange24 !== null && r.priceChange24 !== undefined ? r.priceChange24.toFixed(2) + '%' : 'N/A'}</td>
                <td class="number">$${r.liquidityUsd?.toLocaleString() || 'N/A'}</td>
                <td class="number">$${r.marketCap?.toLocaleString() || 'N/A'}</td>
                <td class="status-cell" style="color: ${statusTextColor}; font-weight: bold;">${r.status}</td>
            </tr>`;
  }

  html += `
        </tbody>
    </table>
    <div class="summary">
        <h3>🎨 Color Legend</h3>
        <p><span class="match" style="padding: 5px 10px; border-radius: 3px;">MATCH</span> - Data matches within tolerance</p>
        <p><span class="good" style="padding: 5px 10px; border-radius: 3px;">GOOD</span> - Lamboo data is higher than Dexscreener (positive direction)</p>
        <p><span class="mismatch" style="padding: 5px 10px; border-radius: 3px;">MISMATCH</span> - Data differs significantly (negative direction)</p>
        <p><span class="unmatched" style="padding: 5px 10px; border-radius: 3px;">UNMATCHED</span> - Data only available from one source</p>
    </div>
</body>
</html>`;

  return html;
}

(function main() {
  // Resolve paths relative to this file's directory (avoid redeclaring __dirname)
  const baseDir = __dirname;
  const paths = {
    dexsFile: path.join(baseDir, '../results/dexscreenerData.json'),
    lambooFile: path.join(baseDir, '../results/LambooMarketData.json'),
    outJSON: path.join(baseDir, '../results/compareVsDexscreener.json'),
    outCSV: path.join(baseDir, '../reports/compareVsDexscreener.csv'),
  };

  // Load input arrays
  const dexs = loadJSON(paths.dexsFile);
  const lambooRaw = loadJSON(paths.lambooFile);

  // Filter out Lamboo pools with volume = 0
  const lamboo = lambooRaw.filter(pool => {
    const volume = pool.volume || 0;
    return volume > 0;
  });

  const filteredCount = lambooRaw.length - lamboo.length;
  console.log(`Loaded ${dexs.length} Dexscreener records and ${lambooRaw.length} Lamboo records`);
  if (filteredCount > 0) {
    console.log(`Filtered out ${filteredCount} Lamboo pools with volume = 0`);
  }
  console.log(`Using ${lamboo.length} valid Lamboo pools for comparison`);

  // Build maps keyed by normalized pair hash; keep all Dexscreener variants for substring matching
  const mapDexs = new Map();
  const allDexs = [];
  for (const d of Array.isArray(dexs) ? dexs : []) {
    const originalPair = d?.pairAddress ? String(d.pairAddress) : null;
    const key = normalizePairAddress(originalPair);
    if (!key) continue;
    const dexId = normalizeDexId(d?.dexId);
    const originalPairLower = originalPair ? originalPair.toLowerCase() : null;
    const record = { dex: dexId, originalPair, originalPairLower, ...pickDexs24h(d) };
    allDexs.push(record);
    if (!mapDexs.has(key)) mapDexs.set(key, []);
    mapDexs.get(key).push(record);
  }

  const mapLamboo = new Map();
  for (const l of Array.isArray(lamboo) ? lamboo : []) {
    const originalPair = l?.pair_address ? String(l.pair_address) : null;
    const key = normalizePairAddress(originalPair);
    if (!key) continue;
    const dexId = normalizeDexId(l?.dex);
    const originalPairLower = originalPair ? originalPair.toLowerCase() : null;
    mapLamboo.set(key, { dex: dexId, originalPair, originalPairLower, ...pickLamboo24h(l) });
  }

  const tolPct = 10; // percentage tolerance applied across all metrics
  const rows = [];

  for (const [key, l] of mapLamboo.entries()) {
    const lamLower = l?.originalPairLower || null;
    const candidates = mapDexs.get(key) || [];
    let d = null;

    if (lamLower) {
      d =
        candidates.find((item) => item.originalPairLower && item.originalPairLower.includes(lamLower)) ||
        allDexs.find((item) => item.originalPairLower && item.originalPairLower.includes(lamLower)) ||
        null;
    } else if (candidates.length > 0) {
      d = candidates[0];
    }

    const dexName = d?.dex || l?.dex || null;
    const tokenSymbol = d?.tokenSymbol || l?.tokenSymbol || null;
    const tokenName = d?.tokenName || l?.tokenName || null;

    const delta = {
      buys: d && l ? d.buys - l.buys : null,
      sells: d && l ? d.sells - l.sells : null,
      txns: d && l ? d.txns - l.txns : null,
      volume: d && l ? d.volume - l.volume : null,
    };

    const pct = {
      buys: d && l ? pctDiff(d.buys, l.buys) : null,
      sells: d && l ? pctDiff(d.sells, l.sells) : null,
      txns: d && l ? pctDiff(d.txns, l.txns) : null,
      volume: d && l ? pctDiff(d.volume, l.volume) : null,
    };

    let status = 'UNMATCHED';
    if (d && l) {
      const metrics = ['buys', 'sells', 'txns', 'volume'];
      const withinTolerance = metrics.every((metric) => {
        const pctVal = pct[metric];
        return Number.isFinite(pctVal) && Math.abs(pctVal) <= tolPct;
      });

      if (withinTolerance) {
        status = 'MATCH';
      } else {
        const lambooHigherVol = Number.isFinite(pct.volume) && pct.volume > 0; // Lamboo volume > Dexscreener volume
        const lambooHigherTxns = Number.isFinite(pct.txns) && pct.txns > 0; // Lamboo txns > Dexscreener txns

        // If both volume and transactions are higher in Lamboo, it's a positive mismatch
        if (lambooHigherVol && lambooHigherTxns) {
          status = 'GOOD';
        } else {
          status = 'MISMATCH';
        }
      }
    }

    // Skip if all Dexscreener data is null/undefined/zero (no meaningful comparison possible)
    if (!d || (d.buys === 0 && d.sells === 0 && d.volume === 0 && d.txns === 0)) {
      console.log(`Skipping ${tokenSymbol} - No meaningful Dexscreener data available`);
      continue;
    }

    rows.push({
      pairKey: l?.originalPair || key,
      dex: dexName,
      tokenSymbol,
      tokenName,
      dexs: d,
      lamboo: l,
      delta,
      pct,
      status,
      timeframes: [{
        timeframe: '24h',
        status: status
      }],
      dexs_buys24: d?.buys ?? null,
      lamboo_buys24: l?.buys ?? null,
      dexs_sells24: d?.sells ?? null,
      lamboo_sells24: l?.sells ?? null,
      dexs_txns24: d?.txns ?? null,
      lamboo_txns24: l?.txns ?? null,
      dexs_volume24: d?.volume ?? null,
      lamboo_volume24: l?.volume ?? null,
      delta_buys: delta.buys,
      delta_sells: delta.sells,
      delta_txns: delta.txns,
      delta_volume: delta.volume,
      delta_buys_pct: pct.buys,
      delta_sells_pct: pct.sells,
      delta_txns_pct: pct.txns,
      delta_volume_pct: pct.volume,
      priceChange24: d?.priceChange ?? null,
      liquidityUsd: d?.liquidityUsd ?? null,
      marketCap: d?.marketCap ?? null,
    });
  }

  // Sort rows by Lamboo volume descending for consistent output
  rows.sort((a, b) => {
    const volA = Number(a.lamboo_volume24);
    const volB = Number(b.lamboo_volume24);
    const sortA = Number.isFinite(volA) ? volA : -Infinity;
    const sortB = Number.isFinite(volB) ? volB : -Infinity;
    return sortB - sortA;
  });

  // Write JSON report and CSV summary
  fs.writeFileSync(paths.outJSON, JSON.stringify({
    generatedAt: new Date().toISOString(),
    tolerancePercentVolume: tolPct,
    pairs: rows,
  }, null, 2));
  fs.writeFileSync(paths.outCSV, toCSV(rows), 'utf8');

  // Save HTML report with colors
  const htmlContent = toHTML(rows);
  const htmlPath = path.join(baseDir, '../reports/compareVsDexscreener.html');
  fs.writeFileSync(htmlPath, htmlContent, 'utf8');
  console.log(`✅ Saved HTML report to ${htmlPath}`);

  // Summary statistics
  let totalComparisons = 0;
  let matchCount = 0;
  let goodCount = 0;
  let mismatchCount = 0;
  // Statistics by timeframe
  const timeframeStats = {
    '24h': { total: 0, match: 0, good: 0, mismatch: 0 }
  };

  rows.forEach(row => {
    totalComparisons++;

    // Update overall stats
    if (row.status === 'MATCH') matchCount++;
    else if (row.status === 'GOOD') goodCount++;
    else if (row.status === 'MISMATCH') mismatchCount++;

    // Update timeframe stats
    timeframeStats['24h'].total++;
    if (row.status === 'MATCH') timeframeStats['24h'].match++;
    else if (row.status === 'GOOD') timeframeStats['24h'].good++;
    else if (row.status === 'MISMATCH') timeframeStats['24h'].mismatch++;
  });

  console.log('\n📊 Overall Summary Statistics:');
  console.log(`  Total comparisons: ${totalComparisons}`);
  console.log(`  Matches: ${matchCount} (${(matchCount/totalComparisons*100).toFixed(1)}%)`);
  console.log(`  Good (Lamboo higher): ${goodCount} (${(goodCount/totalComparisons*100).toFixed(1)}%)`);
  console.log(`  Mismatches: ${mismatchCount} (${(mismatchCount/totalComparisons*100).toFixed(1)}%)`);

  console.log('\n📈 Statistics by Timeframe:');
  Object.keys(timeframeStats).forEach(tf => {
    const stats = timeframeStats[tf];
    if (stats.total > 0) {
      console.log(`  ${tf.toUpperCase()}:`);
      console.log(`    Total: ${stats.total}`);
      console.log(`    Match: ${stats.match} (${(stats.match/stats.total*100).toFixed(1)}%)`);
      console.log(`    Good: ${stats.good} (${(stats.good/stats.total*100).toFixed(1)}%)`);
      console.log(`    Mismatch: ${stats.mismatch} (${(stats.mismatch/stats.total*100).toFixed(1)}%)`);
    }
  });
})();

